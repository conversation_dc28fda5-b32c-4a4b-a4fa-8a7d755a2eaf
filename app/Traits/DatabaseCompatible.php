<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait DatabaseCompatible
{
    /**
     * Get the appropriate date format function for the current database driver
     */
    protected function getDateFormatFunction(string $format): string
    {
        $driver = DB::connection()->getDriverName();

        return match ($driver) {
            'sqlite' => "strftime('{$this->convertToSqliteFormat($format)}', %s)",
            'mysql', 'mariadb' => "DATE_FORMAT(%s, '{$this->convertToMysqlFormat($format)}')",
            'pgsql' => "TO_CHAR(%s, '{$this->convertToPostgresFormat($format)}')",
            'sqlsrv' => "FORMAT(%s, '{$this->convertToSqlServerFormat($format)}')",
            default => "strftime('{$this->convertToSqliteFormat($format)}', %s)", // Default to SQLite format
        };
    }

    /**
     * Get the appropriate date function for the current database driver
     */
    protected function getDateFunction(): string
    {
        $driver = DB::connection()->getDriverName();
        
        return match ($driver) {
            'sqlite' => 'date(%s)',
            'mysql', 'mariadb' => 'DATE(%s)',
            'pgsql' => 'DATE(%s)',
            'sqlsrv' => 'CAST(%s AS DATE)',
            default => 'date(%s)', // Default to SQLite format
        };
    }

    /**
     * Convert to SQLite strftime format
     */
    private function convertToSqliteFormat(string $format): string
    {
        // Input format is already in strftime format, just return as-is
        return $format;
    }

    /**
     * Convert strftime format to MySQL DATE_FORMAT
     */
    private function convertToMysqlFormat(string $format): string
    {
        $conversions = [
            '%Y' => '%Y',
            '%m' => '%m',
            '%d' => '%d',
            '%H' => '%H',
            '%M' => '%i',
            '%S' => '%s',
        ];

        return str_replace(array_keys($conversions), array_values($conversions), $format);
    }

    /**
     * Convert strftime format to PostgreSQL format
     */
    private function convertToPostgresFormat(string $format): string
    {
        $conversions = [
            '%Y' => 'YYYY',
            '%m' => 'MM',
            '%d' => 'DD',
            '%H' => 'HH24',
            '%M' => 'MI',
            '%S' => 'SS',
        ];

        return str_replace(array_keys($conversions), array_values($conversions), $format);
    }

    /**
     * Convert strftime format to SQL Server format
     */
    private function convertToSqlServerFormat(string $format): string
    {
        $conversions = [
            '%Y-%m-%d' => 'yyyy-MM-dd',
            '%Y-%m' => 'yyyy-MM',
            '%Y' => 'yyyy',
            '%m' => 'MM',
            '%d' => 'dd',
        ];

        return $conversions[$format] ?? $format;
    }

    /**
     * Execute a raw select with date formatting
     */
    protected function selectRawWithDateFormat(string $column, string $format, string $alias): string
    {
        $driver = DB::connection()->getDriverName();

        $sql = match ($driver) {
            'sqlite' => "strftime('{$format}', {$column}) as {$alias}",
            'mysql', 'mariadb' => "DATE_FORMAT({$column}, '{$this->convertToMysqlFormat($format)}') as {$alias}",
            'pgsql' => "TO_CHAR({$column}, '{$this->convertToPostgresFormat($format)}') as {$alias}",
            'sqlsrv' => "FORMAT({$column}, '{$this->convertToSqlServerFormat($format)}') as {$alias}",
            default => "strftime('{$format}', {$column}) as {$alias}", // Default to SQLite format
        };

        return $sql;
    }

    /**
     * Execute a raw select with date function
     */
    protected function selectRawWithDate(string $column, string $alias): string
    {
        $driver = DB::connection()->getDriverName();

        $sql = match ($driver) {
            'sqlite' => "date({$column}) as {$alias}",
            'mysql', 'mariadb' => "DATE({$column}) as {$alias}",
            'pgsql' => "DATE({$column}) as {$alias}",
            'sqlsrv' => "CAST({$column} AS DATE) as {$alias}",
            default => "date({$column}) as {$alias}", // Default to SQLite format
        };

        return $sql;
    }
}
