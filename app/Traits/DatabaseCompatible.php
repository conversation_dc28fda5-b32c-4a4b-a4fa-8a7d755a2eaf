<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait DatabaseCompatible
{
    /**
     * Get the appropriate date format function for the current database driver
     */
    protected function getDateFormatFunction(string $format): string
    {
        $driver = DB::connection()->getDriverName();
        
        return match ($driver) {
            'sqlite' => "strftime('{$format}', %s)",
            'mysql', 'mariadb' => "DATE_FORMAT(%s, '{$format}')",
            'pgsql' => "TO_CHAR(%s, '{$this->convertToPostgresFormat($format)}')",
            'sqlsrv' => "FORMAT(%s, '{$this->convertToSqlServerFormat($format)}')",
            default => "strftime('{$format}', %s)", // Default to SQLite format
        };
    }

    /**
     * Get the appropriate date function for the current database driver
     */
    protected function getDateFunction(): string
    {
        $driver = DB::connection()->getDriverName();
        
        return match ($driver) {
            'sqlite' => 'date(%s)',
            'mysql', 'mariadb' => 'DATE(%s)',
            'pgsql' => 'DATE(%s)',
            'sqlsrv' => 'CAST(%s AS DATE)',
            default => 'date(%s)', // Default to SQLite format
        };
    }

    /**
     * Convert strftime format to PostgreSQL format
     */
    private function convertToPostgresFormat(string $format): string
    {
        $conversions = [
            '%Y' => 'YYYY',
            '%m' => 'MM',
            '%d' => 'DD',
            '%H' => 'HH24',
            '%M' => 'MI',
            '%S' => 'SS',
        ];

        return str_replace(array_keys($conversions), array_values($conversions), $format);
    }

    /**
     * Convert strftime format to SQL Server format
     */
    private function convertToSqlServerFormat(string $format): string
    {
        $conversions = [
            '%Y-%m-%d' => 'yyyy-MM-dd',
            '%Y-%m' => 'yyyy-MM',
            '%Y' => 'yyyy',
            '%m' => 'MM',
            '%d' => 'dd',
        ];

        return $conversions[$format] ?? $format;
    }

    /**
     * Execute a raw select with date formatting
     */
    protected function selectRawWithDateFormat(string $column, string $format, string $alias): string
    {
        $formatFunction = $this->getDateFormatFunction($format);
        return sprintf($formatFunction, $column) . " as {$alias}";
    }

    /**
     * Execute a raw select with date function
     */
    protected function selectRawWithDate(string $column, string $alias): string
    {
        $dateFunction = $this->getDateFunction();
        return sprintf($dateFunction, $column) . " as {$alias}";
    }
}
