<?php

namespace App\Filament\Pages;

use App\Models\User;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PopularExplorers extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Scoreboard';

    protected static ?string $navigationLabel = 'Popular Explorers';

    protected static ?string $title = 'Most Active Explorers';

    protected static ?int $navigationSort = -1;

    protected static string $view = 'filament.pages.scoreboard';

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Widgets\ExplorerStatsWidget::class,
            \App\Filament\Widgets\ExplorerActivityChart::class,
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('rank')
                    ->label('#')
                    ->getStateUsing(function ($record, $rowLoop) {
                        return $rowLoop->iteration;
                    })
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Explorer')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('points')
                    ->label('Total Points')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => number_format($state))
                    ->alignEnd()
                    ->badge()
                    ->color('success'),
                Tables\Columns\TextColumn::make('lighthouse_visits_count')
                    ->label('Total Visits')
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('unique_lighthouses_count')
                    ->label('Unique Lighthouses')
                    ->getStateUsing(function ($record) {
                        return $record->lighthouseVisits()
                            ->distinct('lighthouse_id')
                            ->count();
                    })
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('average_points_per_visit')
                    ->label('Avg Points/Visit')
                    ->getStateUsing(function ($record) {
                        $totalVisits = $record->lighthouse_visits_count;
                        if ($totalVisits > 0) {
                            return number_format($record->points / $totalVisits, 1);
                        }
                        return '0';
                    })
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('last_visit')
                    ->label('Last Visit')
                    ->getStateUsing(function ($record) {
                        $lastVisit = $record->lighthouseVisits()
                            ->latest('visited_at')
                            ->first();
                        return $lastVisit ? $lastVisit->visited_at->diffForHumans() : 'Never';
                    })
                    ->color(function ($record) {
                        $lastVisit = $record->lighthouseVisits()
                            ->latest('visited_at')
                            ->first();
                        if (!$lastVisit) return 'gray';
                        
                        $daysSinceLastVisit = $lastVisit->visited_at->diffInDays(now());
                        if ($daysSinceLastVisit <= 7) return 'success';
                        if ($daysSinceLastVisit <= 30) return 'warning';
                        return 'danger';
                    }),
                Tables\Columns\TextColumn::make('favorite_access_type')
                    ->label('Favorite Access')
                    ->getStateUsing(function ($record) {
                        $favoriteType = $record->lighthouseVisits()
                            ->join('lighthouses', 'lighthouse_visits.lighthouse_id', '=', 'lighthouses.id')
                            ->selectRaw('lighthouses.access_type, COUNT(*) as count')
                            ->groupBy('lighthouses.access_type')
                            ->orderByDesc('count')
                            ->first();
                        
                        return $favoriteType ? ucfirst($favoriteType->access_type) : 'N/A';
                    })
                    ->badge()
                    ->color(fn (string $state): string => match (strtolower($state)) {
                        'sea' => 'danger',
                        'walk' => 'warning', 
                        'car' => 'success',
                        'multiple' => 'info',
                        default => 'gray',
                    }),
            ])
            ->defaultSort('points', 'desc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }

    protected function getTableQuery(): Builder
    {
        return User::query()
            ->withCount('lighthouseVisits')
            ->where('lighthouse_visits_count', '>', 0)
            ->orderByDesc('points')
            ->orderByDesc('lighthouse_visits_count');
    }
}
