<?php

namespace App\Filament\Widgets;

use App\Models\LighthouseVisit;
use App\Traits\DatabaseCompatible;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class LighthouseVisitsChart extends ChartWidget
{
    use DatabaseCompatible;

    protected static ?string $heading = 'Lighthouse Visits Trend';

    protected static ?int $sort = 0;

    protected int|string|array $columnSpan = 'full';

    public ?string $filter = '30days';

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '3months' => 'Last 3 months',
            '6months' => 'Last 6 months',
            '12months' => 'Last 12 months',
        ];
    }

    protected function getData(): array
    {
        $days = match ($this->filter) {
            '7days' => 7,
            '30days' => 30,
            '3months' => 90,
            '6months' => 180,
            '12months' => 365,
            default => 30,
        };

        $data = LighthouseVisit::query()
            ->selectRaw($this->selectRawWithDate('visited_at', 'date'))
            ->selectRaw('COUNT(DISTINCT lighthouse_id) as unique_lighthouses')
            ->selectRaw('COUNT(DISTINCT user_id) as unique_visitors')
            ->selectRaw('COUNT(*) as total_visits')
            ->where('visited_at', '>=', now()->subDays($days))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Total Visits',
                    'data' => $data->pluck('total_visits')->toArray(),
                    'borderColor' => 'rgb(59, 130, 246)', // Blue
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => 'start',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Unique Lighthouses',
                    'data' => $data->pluck('unique_lighthouses')->toArray(),
                    'borderColor' => 'rgb(16, 185, 129)', // Green
                    'backgroundColor' => 'transparent',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Unique Visitors',
                    'data' => $data->pluck('unique_visitors')->toArray(),
                    'borderColor' => 'rgb(245, 158, 11)', // Orange
                    'backgroundColor' => 'transparent',
                    'tension' => 0.3,
                ],
            ],
            'labels' => $data->pluck('date')->map(function ($date) {
                return Carbon::parse($date)->format('M d');
            })->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }
}
