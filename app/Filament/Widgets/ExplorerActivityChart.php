<?php

namespace App\Filament\Widgets;

use App\Models\LighthouseVisit;
use App\Models\User;
use App\Traits\DatabaseCompatible;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class ExplorerActivityChart extends ChartWidget
{
    use DatabaseCompatible;

    protected static ?string $heading = 'Explorer Activity Trends';

    protected static ?int $sort = -4;

    protected int|string|array $columnSpan = 'full';

    protected static ?string $maxHeight = '400px';

    public ?string $filter = '30days';

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '3months' => 'Last 3 months',
            '6months' => 'Last 6 months',
            '12months' => 'Last 12 months',
        ];
    }

    protected function getData(): array
    {
        $days = match ($this->filter) {
            '7days' => 7,
            '30days' => 30,
            '3months' => 90,
            '6months' => 180,
            '12months' => 365,
            default => 30,
        };

        // Get date format based on the period (SQLite compatible)
        $dateFormat = match ($this->filter) {
            '7days' => '%Y-%m-%d',
            '30days' => '%Y-%m-%d',
            '3months' => '%Y-%m-%d',
            '6months' => '%Y-%m',
            '12months' => '%Y-%m',
            default => '%Y-%m-%d',
        };

        // Get visit activity data
        $visitData = LighthouseVisit::selectRaw($this->selectRawWithDateFormat('visited_at', $dateFormat, 'date'))
            ->selectRaw('COUNT(*) as total_visits')
            ->selectRaw('COUNT(DISTINCT user_id) as unique_explorers')
            ->selectRaw('COUNT(DISTINCT lighthouse_id) as unique_lighthouses')
            ->where('visited_at', '>=', now()->subDays($days))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Get new explorer registrations (users who made their first visit)
        $newExplorerData = DB::table('lighthouse_visits as lv1')
            ->selectRaw($this->selectRawWithDateFormat('lv1.visited_at', $dateFormat, 'date'))
            ->selectRaw('COUNT(DISTINCT lv1.user_id) as new_explorers')
            ->leftJoin('lighthouse_visits as lv2', function($join) {
                $join->on('lv1.user_id', '=', 'lv2.user_id')
                     ->whereRaw('lv2.visited_at < lv1.visited_at');
            })
            ->whereNull('lv2.user_id')
            ->where('lv1.visited_at', '>=', now()->subDays($days))
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('new_explorers', 'date');

        // Create labels and fill missing dates with zeros
        $labels = [];
        $totalVisits = [];
        $uniqueExplorers = [];
        $uniqueLighthouses = [];
        $newExplorers = [];

        // Generate all dates in the range
        $startDate = now()->subDays($days);
        $endDate = now();
        
        $period = match ($this->filter) {
            '6months', '12months' => 'month',
            default => 'day',
        };

        if ($period === 'month') {
            $current = $startDate->copy()->startOfMonth();
            while ($current <= $endDate) {
                $dateKey = $current->format('Y-m');
                $labels[] = $current->format('M Y');
                
                $visitRecord = $visitData->firstWhere('date', $dateKey);
                $totalVisits[] = $visitRecord ? $visitRecord->total_visits : 0;
                $uniqueExplorers[] = $visitRecord ? $visitRecord->unique_explorers : 0;
                $uniqueLighthouses[] = $visitRecord ? $visitRecord->unique_lighthouses : 0;
                $newExplorers[] = $newExplorerData->get($dateKey, 0);
                
                $current->addMonth();
            }
        } else {
            $current = $startDate->copy();
            while ($current <= $endDate) {
                $dateKey = $current->format('Y-m-d');
                $labels[] = $current->format('M j');
                
                $visitRecord = $visitData->firstWhere('date', $dateKey);
                $totalVisits[] = $visitRecord ? $visitRecord->total_visits : 0;
                $uniqueExplorers[] = $visitRecord ? $visitRecord->unique_explorers : 0;
                $uniqueLighthouses[] = $visitRecord ? $visitRecord->unique_lighthouses : 0;
                $newExplorers[] = $newExplorerData->get($dateKey, 0);
                
                $current->addDay();
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Total Visits',
                    'data' => $totalVisits,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Active Explorers',
                    'data' => $uniqueExplorers,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'New Explorers',
                    'data' => $newExplorers,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
            ],
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
        ];
    }
}
