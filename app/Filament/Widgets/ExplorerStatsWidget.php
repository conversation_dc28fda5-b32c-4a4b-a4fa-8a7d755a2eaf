<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\LighthouseVisit;
use App\Traits\DatabaseCompatible;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class ExplorerStatsWidget extends BaseWidget
{
    use DatabaseCompatible;

    protected static ?int $sort = -5;

    protected int|string|array $columnSpan = 'full';

    protected function getStats(): array
    {
        // Get total active explorers (users with at least one visit)
        $totalActiveExplorers = User::whereHas('lighthouseVisits')->count();

        // Get total points distributed
        $totalPointsDistributed = User::sum('points');

        // Get average points per explorer
        $averagePointsPerExplorer = $totalActiveExplorers > 0 
            ? round($totalPointsDistributed / $totalActiveExplorers, 1)
            : 0;

        // Get most active explorer
        $mostActiveExplorer = User::withCount('lighthouseVisits')
            ->orderByDesc('lighthouse_visits_count')
            ->first();

        // Get recent activity (visits in last 30 days)
        $recentVisits = LighthouseVisit::where('visited_at', '>=', now()->subDays(30))->count();

        // Get explorer activity trend (last 7 days vs previous 7 days)
        $lastWeekVisits = LighthouseVisit::where('visited_at', '>=', now()->subDays(7))->count();
        $previousWeekVisits = LighthouseVisit::whereBetween('visited_at', [
            now()->subDays(14),
            now()->subDays(7)
        ])->count();

        $activityTrend = $previousWeekVisits > 0 
            ? round((($lastWeekVisits - $previousWeekVisits) / $previousWeekVisits) * 100, 1)
            : ($lastWeekVisits > 0 ? 100 : 0);

        // Get top access type preference
        $topAccessType = DB::table('lighthouse_visits')
            ->join('lighthouses', 'lighthouse_visits.lighthouse_id', '=', 'lighthouses.id')
            ->select('lighthouses.access_type', DB::raw('COUNT(*) as count'))
            ->groupBy('lighthouses.access_type')
            ->orderByDesc('count')
            ->first();

        // Get monthly activity chart data for the trend
        $monthlyData = LighthouseVisit::selectRaw($this->selectRawWithDateFormat('visited_at', '%Y-%m', 'month') . ', COUNT(*) as visits')
            ->where('visited_at', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('visits')
            ->toArray();

        return [
            Stat::make('Active Explorers', number_format($totalActiveExplorers))
                ->description('Users with lighthouse visits')
                ->descriptionIcon('heroicon-m-users')
                ->chart($monthlyData)
                ->color('primary'),

            Stat::make('Total Points Earned', number_format($totalPointsDistributed))
                ->description("Avg: {$averagePointsPerExplorer} points per explorer")
                ->descriptionIcon('heroicon-m-trophy')
                ->color('success'),

            Stat::make('Most Active Explorer', $mostActiveExplorer?->name ?? 'N/A')
                ->description($mostActiveExplorer ? number_format($mostActiveExplorer->lighthouse_visits_count) . ' visits' : '')
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),

            Stat::make('Recent Activity', number_format($recentVisits))
                ->description($activityTrend >= 0 ? "+{$activityTrend}% vs last week" : "{$activityTrend}% vs last week")
                ->descriptionIcon($activityTrend >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($activityTrend >= 0 ? 'success' : 'danger'),

            Stat::make('Favorite Access Type', $topAccessType ? ucfirst($topAccessType->access_type) : 'N/A')
                ->description($topAccessType ? number_format($topAccessType->count) . ' visits' : '')
                ->descriptionIcon('heroicon-m-map')
                ->color(match($topAccessType?->access_type ?? '') {
                    'sea' => 'danger',
                    'walk' => 'warning',
                    'car' => 'success', 
                    'multiple' => 'info',
                    default => 'gray',
                }),
        ];
    }
}
